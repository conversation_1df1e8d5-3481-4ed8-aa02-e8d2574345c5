'use client'

import { useState } from 'react'
import Navigation from '@/components/Navigation'
import FormBuilder from '@/components/FormBuilder'
import { useFormBuilderStore } from '@/store/formBuilderStore'
import { useCreateForm } from '@/hooks/useApi'
import { useRouter } from 'next/navigation'
import { Save, Eye, Settings } from 'lucide-react'

export default function CreateFormPage() {
  const router = useRouter()
  const { 
    currentForm, 
    isPreviewMode, 
    togglePreviewMode,
    isDirty,
    resetForm 
  } = useFormBuilderStore()
  
  const { execute: createForm, loading: saving } = useCreateForm()
  const [showSettings, setShowSettings] = useState(false)

  const handleSave = async () => {
    if (!currentForm) return

    try {
      const formData = {
        ...currentForm,
        orgId: 'org-1', // Demo organization
      }
      
      const savedForm = await createForm(formData)
      if (savedForm) {
        resetForm()
        router.push(`/forms/${savedForm.id}`)
      }
    } catch (error) {
      console.error('Error saving form:', error)
      // In a real app, you'd show a toast notification here
    }
  }

  const handlePreview = () => {
    togglePreviewMode()
  }

  return (
    <div className="min-h-screen bg-base-100">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Create New Form</h1>
            <p className="text-base-content/70 mt-2">
              Build your custom form with drag-and-drop simplicity
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Form Settings Button */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={`btn btn-outline ${showSettings ? 'btn-active' : ''}`}
            >
              <Settings size={20} />
              Settings
            </button>
            
            {/* Preview Toggle */}
            <button
              onClick={handlePreview}
              className={`btn ${isPreviewMode ? 'btn-secondary' : 'btn-outline'}`}
            >
              <Eye size={20} />
              {isPreviewMode ? 'Exit Preview' : 'Preview'}
            </button>
            
            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={!isDirty || saving || !currentForm?.title}
              className="btn btn-primary"
            >
              {saving ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Saving...
                </>
              ) : (
                <>
                  <Save size={20} />
                  Save Form
                </>
              )}
            </button>
          </div>
        </div>

        {/* Form Settings Panel */}
        {showSettings && (
          <div className="card bg-base-200 shadow-xl mb-8">
            <div className="card-body">
              <h2 className="card-title">Form Settings</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Form Title *</span>
                  </label>
                  <input
                    type="text"
                    placeholder="Enter form title"
                    className="input input-bordered"
                    value={currentForm?.title || ''}
                    onChange={(e) => useFormBuilderStore.getState().updateFormSettings({ title: e.target.value })}
                  />
                </div>
                
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Form Description</span>
                  </label>
                  <textarea
                    placeholder="Enter form description"
                    className="textarea textarea-bordered"
                    value={currentForm?.description || ''}
                    onChange={(e) => useFormBuilderStore.getState().updateFormSettings({ description: e.target.value })}
                  />
                </div>
                
                <div className="form-control">
                  <label className="cursor-pointer label">
                    <span className="label-text">Form is Active</span>
                    <input
                      type="checkbox"
                      className="toggle toggle-primary"
                      checked={currentForm?.settings.isActive || false}
                      onChange={(e) => useFormBuilderStore.getState().updateFormSettings({ 
                        settings: { 
                          ...currentForm?.settings, 
                          isActive: e.target.checked 
                        } 
                      })}
                    />
                  </label>
                </div>
                
                <div className="form-control">
                  <label className="cursor-pointer label">
                    <span className="label-text">Allow Draft Responses</span>
                    <input
                      type="checkbox"
                      className="toggle toggle-secondary"
                      checked={currentForm?.settings.allowDrafts || false}
                      onChange={(e) => useFormBuilderStore.getState().updateFormSettings({ 
                        settings: { 
                          ...currentForm?.settings, 
                          allowDrafts: e.target.checked 
                        } 
                      })}
                    />
                  </label>
                </div>
                
                <div className="form-control">
                  <label className="cursor-pointer label">
                    <span className="label-text">Require Authentication</span>
                    <input
                      type="checkbox"
                      className="toggle toggle-accent"
                      checked={currentForm?.settings.requireAuth || false}
                      onChange={(e) => useFormBuilderStore.getState().updateFormSettings({ 
                        settings: { 
                          ...currentForm?.settings, 
                          requireAuth: e.target.checked 
                        } 
                      })}
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Form Builder */}
        <FormBuilder />
      </div>
    </div>
  )
}

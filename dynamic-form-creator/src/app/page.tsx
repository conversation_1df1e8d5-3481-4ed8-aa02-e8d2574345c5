'use client'

import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Plus, FormInput, Users, BarChart3 } from 'lucide-react'
import { useForms, useResponses } from '@/hooks/useApi'
import { useEffect, useState } from 'react'

export default function Home() {
  const { data: forms, loading: formsLoading, execute: fetchForms } = useForms()
  const { data: responses, loading: responsesLoading, execute: fetchResponses } = useResponses()
  const [stats, setStats] = useState({
    totalForms: 0,
    totalResponses: 0,
    activeForms: 0,
  })

  useEffect(() => {
    // Fetch forms for demo organization
    fetchForms('org-1')
    fetchResponses()
  }, [fetchForms, fetchResponses])

  useEffect(() => {
    if (forms && responses) {
      setStats({
        totalForms: forms.length,
        totalResponses: responses.length,
        activeForms: forms.filter(form => form.settings.isActive).length,
      })
    }
  }, [forms, responses])
  return (
    <div className="min-h-screen bg-base-100">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="hero bg-gradient-to-r from-primary to-secondary text-primary-content rounded-lg mb-8">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="mb-5 text-5xl font-bold">Dynamic Form Creator</h1>
              <p className="mb-5">
                Create, manage, and collect responses from custom forms for your organization.
                Build powerful forms with drag-and-drop simplicity.
              </p>
              <Link href="/forms/create" className="btn btn-accent">
                <Plus size={20} />
                Create Your First Form
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="stats shadow w-full mb-8">
          <div className="stat">
            <div className="stat-figure text-primary">
              <FormInput size={32} />
            </div>
            <div className="stat-title">Total Forms</div>
            <div className="stat-value text-primary">
              {formsLoading ? (
                <span className="loading loading-spinner loading-sm"></span>
              ) : (
                stats.totalForms
              )}
            </div>
            <div className="stat-desc">Forms created</div>
          </div>

          <div className="stat">
            <div className="stat-figure text-secondary">
              <Users size={32} />
            </div>
            <div className="stat-title">Total Responses</div>
            <div className="stat-value text-secondary">
              {responsesLoading ? (
                <span className="loading loading-spinner loading-sm"></span>
              ) : (
                stats.totalResponses
              )}
            </div>
            <div className="stat-desc">Responses collected</div>
          </div>

          <div className="stat">
            <div className="stat-figure text-accent">
              <BarChart3 size={32} />
            </div>
            <div className="stat-title">Active Forms</div>
            <div className="stat-value text-accent">
              {formsLoading ? (
                <span className="loading loading-spinner loading-sm"></span>
              ) : (
                stats.activeForms
              )}
            </div>
            <div className="stat-desc">Currently active</div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <FormInput size={24} />
                Create New Form
              </h2>
              <p>Build a custom form with our intuitive drag-and-drop builder.</p>
              <div className="card-actions justify-end">
                <Link href="/forms/create" className="btn btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>

          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <Users size={24} />
                View Responses
              </h2>
              <p>Review and analyze responses from your published forms.</p>
              <div className="card-actions justify-end">
                <Link href="/responses" className="btn btn-secondary">
                  View All
                </Link>
              </div>
            </div>
          </div>

          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <BarChart3 size={24} />
                Analytics
              </h2>
              <p>Get insights into form performance and user engagement.</p>
              <div className="card-actions justify-end">
                <Link href="/analytics" className="btn btn-accent">
                  View Analytics
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card bg-base-200 shadow-xl">
          <div className="card-body">
            <h2 className="card-title mb-4">Recent Forms</h2>
            {formsLoading ? (
              <div className="text-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
                <p className="mt-4">Loading forms...</p>
              </div>
            ) : forms && forms.length > 0 ? (
              <div className="space-y-4">
                {forms.slice(0, 3).map((form) => (
                  <div key={form.id} className="flex items-center justify-between p-4 bg-base-100 rounded-lg">
                    <div>
                      <h3 className="font-semibold">{form.title}</h3>
                      <p className="text-sm text-base-content/70">{form.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className={`badge ${form.settings.isActive ? 'badge-success' : 'badge-warning'}`}>
                          {form.settings.isActive ? 'Active' : 'Inactive'}
                        </span>
                        <span className="text-xs text-base-content/50">
                          {form.fields.length} fields
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Link href={`/forms/${form.id}/edit`} className="btn btn-sm btn-outline">
                        Edit
                      </Link>
                      <Link href={`/forms/${form.id}`} className="btn btn-sm btn-primary">
                        View
                      </Link>
                    </div>
                  </div>
                ))}
                {forms.length > 3 && (
                  <div className="text-center pt-4">
                    <Link href="/forms" className="btn btn-outline">
                      View All Forms ({forms.length})
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-base-content/60">
                <FormInput size={48} className="mx-auto mb-4 opacity-50" />
                <p className="mb-4">No forms created yet. Create your first form to get started!</p>
                <Link href="/forms/create" className="btn btn-primary">
                  Create Your First Form
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}

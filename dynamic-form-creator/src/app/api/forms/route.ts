import { NextRequest, NextResponse } from 'next/server'
import { formsDB } from '@/lib/database'
import { FormTemplateSchema } from '@/types/form'

// GET /api/forms - Get all forms for an organization
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId')

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      )
    }

    const forms = await formsDB.getByOrgId(orgId)
    return NextResponse.json({ forms })
  } catch (error) {
    console.error('Error fetching forms:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/forms - Create a new form
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the form data
    const validationResult = FormTemplateSchema.omit({
      id: true,
      createdAt: true,
      updatedAt: true,
      version: true,
    }).safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid form data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const newForm = await formsDB.create(validationResult.data)
    return NextResponse.json({ form: newForm }, { status: 201 })
  } catch (error) {
    console.error('Error creating form:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

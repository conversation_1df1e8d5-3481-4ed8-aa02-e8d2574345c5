import { NextRequest, NextResponse } from 'next/server'
import { responsesDB } from '@/lib/database'
import { FormResponseSchema } from '@/types/form'

// GET /api/responses - Get responses with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const formId = searchParams.get('formId')
    const memberId = searchParams.get('memberId')

    let responses

    if (formId && memberId) {
      // Get specific member's response for a form
      const response = await responsesDB.getByFormAndMember(formId, memberId)
      responses = response ? [response] : []
    } else if (formId) {
      // Get all responses for a form
      responses = await responsesDB.getByFormId(formId)
    } else if (memberId) {
      // Get all responses by a member
      responses = await responsesDB.getByMemberId(memberId)
    } else {
      // Get all responses (admin view)
      responses = await responsesDB.getAll()
    }

    return NextResponse.json({ responses })
  } catch (error) {
    console.error('Error fetching responses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/responses - Create a new response
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the response data
    const validationResult = FormResponseSchema.omit({
      id: true,
      createdAt: true,
      updatedAt: true,
    }).safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid response data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const newResponse = await responsesDB.create(validationResult.data)
    return NextResponse.json({ response: newResponse }, { status: 201 })
  } catch (error) {
    console.error('Error creating response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

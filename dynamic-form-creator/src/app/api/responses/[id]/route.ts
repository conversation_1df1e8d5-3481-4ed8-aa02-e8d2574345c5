import { NextRequest, NextResponse } from 'next/server'
import { responsesDB } from '@/lib/database'
import { FormResponseSchema } from '@/types/form'

// GET /api/responses/[id] - Get a specific response
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = await responsesDB.getById(params.id)
    
    if (!response) {
      return NextResponse.json(
        { error: 'Response not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ response })
  } catch (error) {
    console.error('Error fetching response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/responses/[id] - Update a response
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // Validate the response data
    const validationResult = FormResponseSchema.partial().safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid response data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const updatedResponse = await responsesDB.update(params.id, validationResult.data)
    
    if (!updatedResponse) {
      return NextResponse.json(
        { error: 'Response not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ response: updatedResponse })
  } catch (error) {
    console.error('Error updating response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/responses/[id] - Delete a response
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await responsesDB.delete(params.id)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Response not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Response deleted successfully' })
  } catch (error) {
    console.error('Error deleting response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

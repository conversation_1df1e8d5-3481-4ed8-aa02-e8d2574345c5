// Simple in-memory database simulation for demo purposes
// In a real application, this would be replaced with a proper database like PostgreSQL, MongoDB, etc.

import { FormTemplate, FormResponse } from '@/types/form'

// In-memory storage
let forms: FormTemplate[] = []
let responses: FormResponse[] = []

// Helper function to generate IDs
const generateId = () => Math.random().toString(36).substr(2, 9)

// Forms CRUD operations
export const formsDB = {
  // Create a new form
  create: async (formData: Omit<FormTemplate, 'id' | 'createdAt' | 'updatedAt' | 'version'>): Promise<FormTemplate> => {
    const now = new Date()
    const newForm: FormTemplate = {
      ...formData,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
      version: 1,
    }
    forms.push(newForm)
    return newForm
  },

  // Get all forms for an organization
  getByOrgId: async (orgId: string): Promise<FormTemplate[]> => {
    return forms.filter(form => form.orgId === orgId)
  },

  // Get a specific form by ID
  getById: async (id: string): Promise<FormTemplate | null> => {
    return forms.find(form => form.id === id) || null
  },

  // Update a form
  update: async (id: string, updates: Partial<FormTemplate>): Promise<FormTemplate | null> => {
    const formIndex = forms.findIndex(form => form.id === id)
    if (formIndex === -1) return null

    const updatedForm = {
      ...forms[formIndex],
      ...updates,
      updatedAt: new Date(),
      version: forms[formIndex].version + 1,
    }
    forms[formIndex] = updatedForm
    return updatedForm
  },

  // Delete a form
  delete: async (id: string): Promise<boolean> => {
    const formIndex = forms.findIndex(form => form.id === id)
    if (formIndex === -1) return false

    forms.splice(formIndex, 1)
    // Also delete all responses for this form
    responses = responses.filter(response => response.formId !== id)
    return true
  },

  // Get all forms (for admin purposes)
  getAll: async (): Promise<FormTemplate[]> => {
    return forms
  },
}

// Form Responses CRUD operations
export const responsesDB = {
  // Create a new response
  create: async (responseData: Omit<FormResponse, 'id' | 'createdAt' | 'updatedAt'>): Promise<FormResponse> => {
    const now = new Date()
    const newResponse: FormResponse = {
      ...responseData,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    }
    responses.push(newResponse)
    return newResponse
  },

  // Get all responses for a form
  getByFormId: async (formId: string): Promise<FormResponse[]> => {
    return responses.filter(response => response.formId === formId)
  },

  // Get a specific response by ID
  getById: async (id: string): Promise<FormResponse | null> => {
    return responses.find(response => response.id === id) || null
  },

  // Get responses by member ID
  getByMemberId: async (memberId: string): Promise<FormResponse[]> => {
    return responses.filter(response => response.memberId === memberId)
  },

  // Get a specific member's response for a form
  getByFormAndMember: async (formId: string, memberId: string): Promise<FormResponse | null> => {
    return responses.find(response => 
      response.formId === formId && response.memberId === memberId
    ) || null
  },

  // Update a response
  update: async (id: string, updates: Partial<FormResponse>): Promise<FormResponse | null> => {
    const responseIndex = responses.findIndex(response => response.id === id)
    if (responseIndex === -1) return null

    const updatedResponse = {
      ...responses[responseIndex],
      ...updates,
      updatedAt: new Date(),
    }
    responses[responseIndex] = updatedResponse
    return updatedResponse
  },

  // Delete a response
  delete: async (id: string): Promise<boolean> => {
    const responseIndex = responses.findIndex(response => response.id === id)
    if (responseIndex === -1) return false

    responses.splice(responseIndex, 1)
    return true
  },

  // Get all responses (for admin purposes)
  getAll: async (): Promise<FormResponse[]> => {
    return responses
  },
}

// Utility functions for demo data
export const seedDatabase = () => {
  // Add some sample forms for demonstration
  const sampleForm1: FormTemplate = {
    id: 'sample-form-1',
    orgId: 'org-1',
    title: 'Contact Information Form',
    description: 'Please provide your contact details',
    fields: [
      {
        id: 'field-1',
        type: 'text',
        label: 'Full Name',
        placeholder: 'Enter your full name',
        validation: { required: true },
        order: 0,
      },
      {
        id: 'field-2',
        type: 'text',
        label: 'Email Address',
        placeholder: 'Enter your email',
        validation: { required: true, pattern: '^[^@]+@[^@]+\\.[^@]+$' },
        order: 1,
      },
      {
        id: 'field-3',
        type: 'number',
        label: 'Phone Number',
        placeholder: 'Enter your phone number',
        validation: { required: false },
        order: 2,
      },
    ],
    settings: {
      isActive: true,
      allowDrafts: true,
      requireAuth: true,
    },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    version: 1,
  }

  const sampleForm2: FormTemplate = {
    id: 'sample-form-2',
    orgId: 'org-1',
    title: 'Feedback Survey',
    description: 'Help us improve our services',
    fields: [
      {
        id: 'field-4',
        type: 'radio',
        label: 'How satisfied are you with our service?',
        options: [
          { label: 'Very Satisfied', value: 'very-satisfied' },
          { label: 'Satisfied', value: 'satisfied' },
          { label: 'Neutral', value: 'neutral' },
          { label: 'Dissatisfied', value: 'dissatisfied' },
          { label: 'Very Dissatisfied', value: 'very-dissatisfied' },
        ],
        validation: { required: true },
        order: 0,
      },
      {
        id: 'field-5',
        type: 'textarea',
        label: 'Additional Comments',
        placeholder: 'Please share any additional feedback...',
        validation: { required: false, maxLength: 500 },
        order: 1,
      },
    ],
    settings: {
      isActive: true,
      allowDrafts: true,
      requireAuth: false,
    },
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    version: 1,
  }

  forms.push(sampleForm1, sampleForm2)
}

// Initialize with sample data
seedDatabase()

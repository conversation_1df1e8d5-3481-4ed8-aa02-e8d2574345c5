import { useState, useCallback } from 'react'
import { FormTemplate, FormResponse } from '@/types/form'

interface ApiResponse<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface ApiHook<T> extends ApiResponse<T> {
  execute: (...args: any[]) => Promise<T | null>
  reset: () => void
}

// Generic API hook
function useApi<T>(apiFunction: (...args: any[]) => Promise<T>): ApiHook<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    try {
      setLoading(true)
      setError(null)
      const result = await apiFunction(...args)
      setData(result)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      return null
    } finally {
      setLoading(false)
    }
  }, [apiFunction])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  return { data, loading, error, execute, reset }
}

// API functions
const api = {
  // Forms API
  forms: {
    getAll: async (orgId: string): Promise<FormTemplate[]> => {
      const response = await fetch(`/api/forms?orgId=${orgId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch forms')
      }
      const data = await response.json()
      return data.forms
    },

    getById: async (id: string): Promise<FormTemplate> => {
      const response = await fetch(`/api/forms/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch form')
      }
      const data = await response.json()
      return data.form
    },

    create: async (formData: Omit<FormTemplate, 'id' | 'createdAt' | 'updatedAt' | 'version'>): Promise<FormTemplate> => {
      const response = await fetch('/api/forms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })
      if (!response.ok) {
        throw new Error('Failed to create form')
      }
      const data = await response.json()
      return data.form
    },

    update: async (id: string, updates: Partial<FormTemplate>): Promise<FormTemplate> => {
      const response = await fetch(`/api/forms/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      if (!response.ok) {
        throw new Error('Failed to update form')
      }
      const data = await response.json()
      return data.form
    },

    delete: async (id: string): Promise<void> => {
      const response = await fetch(`/api/forms/${id}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete form')
      }
    },
  },

  // Responses API
  responses: {
    getAll: async (): Promise<FormResponse[]> => {
      const response = await fetch('/api/responses')
      if (!response.ok) {
        throw new Error('Failed to fetch responses')
      }
      const data = await response.json()
      return data.responses
    },

    getByFormId: async (formId: string): Promise<FormResponse[]> => {
      const response = await fetch(`/api/responses?formId=${formId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch responses')
      }
      const data = await response.json()
      return data.responses
    },

    getByMemberId: async (memberId: string): Promise<FormResponse[]> => {
      const response = await fetch(`/api/responses?memberId=${memberId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch responses')
      }
      const data = await response.json()
      return data.responses
    },

    getById: async (id: string): Promise<FormResponse> => {
      const response = await fetch(`/api/responses/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch response')
      }
      const data = await response.json()
      return data.response
    },

    create: async (responseData: Omit<FormResponse, 'id' | 'createdAt' | 'updatedAt'>): Promise<FormResponse> => {
      const response = await fetch('/api/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(responseData),
      })
      if (!response.ok) {
        throw new Error('Failed to create response')
      }
      const data = await response.json()
      return data.response
    },

    update: async (id: string, updates: Partial<FormResponse>): Promise<FormResponse> => {
      const response = await fetch(`/api/responses/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      if (!response.ok) {
        throw new Error('Failed to update response')
      }
      const data = await response.json()
      return data.response
    },

    delete: async (id: string): Promise<void> => {
      const response = await fetch(`/api/responses/${id}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete response')
      }
    },
  },
}

// Specific hooks for different API operations
export const useForms = () => useApi(api.forms.getAll)
export const useForm = () => useApi(api.forms.getById)
export const useCreateForm = () => useApi(api.forms.create)
export const useUpdateForm = () => useApi(api.forms.update)
export const useDeleteForm = () => useApi(api.forms.delete)

export const useResponses = () => useApi(api.responses.getAll)
export const useFormResponses = () => useApi(api.responses.getByFormId)
export const useMemberResponses = () => useApi(api.responses.getByMemberId)
export const useResponse = () => useApi(api.responses.getById)
export const useCreateResponse = () => useApi(api.responses.create)
export const useUpdateResponse = () => useApi(api.responses.update)
export const useDeleteResponse = () => useApi(api.responses.delete)

export default api

import { z } from 'zod'

// Field Types
export type FieldType =
  | 'text'
  | 'textarea'
  | 'number'
  | 'date'
  | 'dropdown'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'file'

// Validation Rules
export const ValidationRuleSchema = z.object({
  required: z.boolean().default(false),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  pattern: z.string().optional(),
  customMessage: z.string().optional(),
})

export type ValidationRule = z.infer<typeof ValidationRuleSchema>

// Field Configuration
export const FieldConfigSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'number', 'date', 'dropdown', 'multiselect', 'checkbox', 'radio', 'file']),
  label: z.string(),
  placeholder: z.string().optional(),
  defaultValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]).optional(),
  options: z.array(z.object({
    label: z.string(),
    value: z.string(),
  })).optional(),
  validation: ValidationRuleSchema.optional(),
  conditionalLogic: z.object({
    dependsOn: z.string(),
    condition: z.enum(['equals', 'not_equals', 'contains', 'not_contains']),
    value: z.union([z.string(), z.number(), z.boolean()]),
  }).optional(),
  order: z.number(),
})

export type FieldConfig = z.infer<typeof FieldConfigSchema>

// Form Template
export const FormTemplateSchema = z.object({
  id: z.string(),
  orgId: z.string(),
  title: z.string(),
  description: z.string().optional(),
  fields: z.array(FieldConfigSchema),
  settings: z.object({
    isActive: z.boolean().default(true),
    allowDrafts: z.boolean().default(true),
    requireAuth: z.boolean().default(true),
    submitMessage: z.string().optional(),
    redirectUrl: z.string().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
  version: z.number().default(1),
})

export type FormTemplate = z.infer<typeof FormTemplateSchema>

// Form Response
export const FormResponseSchema = z.object({
  id: z.string(),
  formId: z.string(),
  memberId: z.string(),
  data: z.record(z.string(), z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.array(z.string()),
    z.null()
  ])),
  isDraft: z.boolean().default(false),
  submittedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export type FormResponse = z.infer<typeof FormResponseSchema>

// Form Builder State
export interface FormBuilderState {
  currentForm: Partial<FormTemplate>
  selectedField: string | null
  draggedField: FieldConfig | null
  isPreviewMode: boolean
  isDirty: boolean
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasNext: boolean
  hasPrev: boolean
}

// Form Submission Types
export interface FormSubmissionData {
  [fieldId: string]: string | number | boolean | string[] | null
}

export interface FormValidationError {
  fieldId: string
  message: string
}

export interface FormSubmissionResult {
  success: boolean
  errors?: FormValidationError[]
  responseId?: string
}

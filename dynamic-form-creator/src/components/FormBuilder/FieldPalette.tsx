'use client'

import { useDraggable } from '@dnd-kit/core'
import {
  Type,
  AlignLeft,
  Hash,
  Calendar,
  ChevronDown,
  CheckSquare,
  Circle,
  Upload,
  ListChecks
} from 'lucide-react'
import { FieldType } from '@/types/form'

interface FieldTypeConfig {
  type: FieldType
  label: string
  icon: React.ReactNode
  description: string
}

const fieldTypes: FieldTypeConfig[] = [
  {
    type: 'text',
    label: 'Text Input',
    icon: <Type size={20} />,
    description: 'Single line text input'
  },
  {
    type: 'textarea',
    label: 'Text Area',
    icon: <AlignLeft size={20} />,
    description: 'Multi-line text input'
  },
  {
    type: 'number',
    label: 'Number',
    icon: <Hash size={20} />,
    description: 'Numeric input field'
  },
  {
    type: 'date',
    label: 'Date',
    icon: <Calendar size={20} />,
    description: 'Date picker input'
  },
  {
    type: 'dropdown',
    label: 'Dropdown',
    icon: <ChevronDown size={20} />,
    description: 'Select from options'
  },
  {
    type: 'multiselect',
    label: 'Multi-Select',
    icon: <ListChecks size={20} />,
    description: 'Multiple checkbox options'
  },
  {
    type: 'checkbox',
    label: 'Checkbox',
    icon: <CheckSquare size={20} />,
    description: 'Single checkbox field'
  },
  {
    type: 'radio',
    label: 'Radio Button',
    icon: <Circle size={20} />,
    description: 'Single choice selection'
  },
  {
    type: 'file',
    label: 'File Upload',
    icon: <Upload size={20} />,
    description: 'File upload input'
  }
]

interface DraggableFieldProps {
  fieldType: FieldTypeConfig
}

function DraggableField({ fieldType }: DraggableFieldProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `palette-${fieldType.type}`,
    data: {
      type: 'field-type',
      fieldType: fieldType.type,
    },
  })

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`
        card bg-base-100 shadow-sm border border-base-300 cursor-grab active:cursor-grabbing
        hover:shadow-md transition-shadow duration-200
        ${isDragging ? 'opacity-50' : ''}
      `}
    >
      <div className="card-body p-4">
        <div className="flex items-center gap-3">
          <div className="text-primary">
            {fieldType.icon}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-sm">{fieldType.label}</h3>
            <p className="text-xs text-base-content/60">{fieldType.description}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function FieldPalette() {
  return (
    <div className="card bg-base-200 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-lg mb-4">Field Types</h2>
        <p className="text-sm text-base-content/70 mb-6">
          Drag and drop field types to build your form
        </p>
        
        <div className="space-y-3">
          {fieldTypes.map((fieldType) => (
            <DraggableField key={fieldType.type} fieldType={fieldType} />
          ))}
        </div>
        
        <div className="divider"></div>
        
        <div className="text-xs text-base-content/50">
          <p className="mb-2">💡 <strong>Tips:</strong></p>
          <ul className="space-y-1 ml-4">
            <li>• Drag fields to the canvas</li>
            <li>• Click fields to configure</li>
            <li>• Reorder by dragging</li>
            <li>• Use preview to test</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

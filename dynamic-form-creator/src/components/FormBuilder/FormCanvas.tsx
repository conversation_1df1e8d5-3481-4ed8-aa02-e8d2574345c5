'use client'

import { useDroppable } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useFormBuilderStore } from '@/store/formBuilderStore'
import SortableFieldItem from './SortableFieldItem'
import { Plus } from 'lucide-react'

export default function FormCanvas() {
  const { currentForm } = useFormBuilderStore()
  const fields = currentForm?.fields || []

  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: 'form-canvas',
  })

  const fieldIds = fields.map(field => field.id)

  return (
    <div className="card bg-base-100 shadow-xl min-h-[600px]">
      <div className="card-body">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="card-title text-xl">Form Builder</h2>
            <p className="text-base-content/70">
              {currentForm?.title || 'Untitled Form'}
            </p>
          </div>
          <div className="badge badge-outline">
            {fields.length} field{fields.length !== 1 ? 's' : ''}
          </div>
        </div>

        <div
          ref={setNodeRef}
          className={`
            flex-1 min-h-[400px] border-2 border-dashed rounded-lg p-6 transition-colors
            ${isOver ? 'border-primary bg-primary/5' : 'border-base-300'}
            ${fields.length === 0 ? 'flex items-center justify-center' : ''}
          `}
        >
          {fields.length === 0 ? (
            <div className="text-center text-base-content/50">
              <Plus size={48} className="mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">Start Building Your Form</h3>
              <p className="max-w-md">
                Drag field types from the palette on the left to add them to your form.
                You can reorder fields by dragging them up or down.
              </p>
            </div>
          ) : (
            <SortableContext items={fieldIds} strategy={verticalListSortingStrategy}>
              <div className="space-y-4">
                {fields.map((field) => (
                  <SortableFieldItem key={field.id} field={field} />
                ))}
              </div>
            </SortableContext>
          )}
        </div>

        {fields.length > 0 && (
          <div className="mt-6 p-4 bg-base-200 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-base-content/70">
                Drop new fields here or between existing fields
              </span>
              <span className="text-base-content/50">
                {isOver ? 'Drop to add field' : 'Ready for new fields'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

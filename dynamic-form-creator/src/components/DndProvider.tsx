'use client'

import { Dnd<PERSON>ontext, DragEndEvent, DragOverEvent, DragStartEvent } from '@dnd-kit/core'
import { arrayMove } from '@dnd-kit/sortable'
import { useFormBuilderStore } from '@/store/formBuilderStore'
import { FieldType } from '@/types/form'
import { nanoid } from 'nanoid'

interface DndProviderProps {
  children: React.ReactNode
}

export function DndProvider({ children }: DndProviderProps) {
  const { addField, reorderFields, setDraggedField } = useFormBuilderStore()

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    
    if (active.data.current?.type === 'field') {
      setDraggedField(active.data.current.field)
    }
  }

  const handleDragOver = (event: DragOverEvent) => {
    // Handle drag over logic if needed
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    setDraggedField(null)

    if (!over) return

    // <PERSON>le dropping a field type from palette to canvas
    if (active.data.current?.type === 'field-type' && over.id === 'form-canvas') {
      const fieldType = active.data.current.fieldType as FieldType
      
      const newField = {
        id: nanoid(),
        type: fieldType,
        label: `${fieldType.charAt(0).toUpperCase() + fieldType.slice(1)} Field`,
        placeholder: `Enter ${fieldType}`,
        validation: { required: false },
        order: 0, // Will be updated by the store
      }

      addField(newField)
      return
    }

    // Handle reordering fields within the canvas
    if (active.data.current?.type === 'field' && over.data.current?.type === 'field') {
      const activeField = active.data.current.field
      const overField = over.data.current.field
      
      if (activeField.id !== overField.id) {
        const { currentForm } = useFormBuilderStore.getState()
        if (!currentForm) return

        const fields = currentForm.fields
        const activeIndex = fields.findIndex(f => f.id === activeField.id)
        const overIndex = fields.findIndex(f => f.id === overField.id)

        if (activeIndex !== -1 && overIndex !== -1) {
          const newFields = arrayMove(fields, activeIndex, overIndex)
          reorderFields(newFields)
        }
      }
    }
  }

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {children}
    </DndContext>
  )
}
